import { useCallback, useMemo } from 'react'

import { useQuery } from '@apollo/client'
import { DateTime } from 'luxon'

import { Button, Caps3 } from '@apella/component-library'
import { ToggleableCaseKey } from 'src/components/Keys'
import { useScheduleMetrics } from 'src/modules/daily-metrics/hooks/useScheduleMetrics'
import { useScheduleContext } from 'src/pages/Schedule/ScheduleContextProvider'
import { useScheduleFilterContext } from 'src/pages/Schedule/ScheduleFilterContext'
import { EVENTS } from 'src/utils/analyticsEvents'

import {
  CaseStatusName,
  CaseType,
  RoomStatusName,
} from '../../__generated__/globalTypes'
import { getAnalyticsAddtlTurnoverData } from '../../utils/turnovers'
import { useIsToday } from '../../utils/useCurrentTime'
import { useOpenVideoBlade } from '../../utils/useOpenVideoBlade'
import { ApellaBlock } from '../types'
import { GetLiveFromScheduleEnabled } from './__generated__'
import { EmptyRoomsMessage } from './EmptyRoomsMessage'
import { GET_LIVE_FROM_SCHEDULE_ENABLED } from './queries'
import { ScheduleFilters } from './ScheduleFilters'
import { ScheduleMetrics } from './ScheduleMetrics'
import SchedulePageHeader from './SchedulePageHeader'
import TimelineView from './TimelineView'

export const ScheduleView = ({ blocks }: { blocks: ApellaBlock[] }) => {
  const {
    onClick,
    timelineState,
    isAllEditPage,
    timelineHourBounds,
    filteredTimelineState,
  } = useScheduleContext()

  const {
    roomIds,
    minTime,
    showScheduled,
    siteIds,
    timeRange,
    onChangeTimeRange,
  } = useScheduleFilterContext()

  // The rooms that will be used to calculate the Schedule Metrics.
  const scheduleMetricsRooms = useMemo(() => {
    let newRooms = timelineState.rooms

    // Filter down by rooms
    if (roomIds !== undefined && roomIds.length) {
      newRooms = newRooms.filter((r) => roomIds.includes(r.id))
    }

    return newRooms
  }, [roomIds, timelineState.rooms])

  const scheduleMetrics = useScheduleMetrics({
    minTime,
    apellaCases: scheduleMetricsRooms.flatMap((r) => r.cases),
  })

  const { data: getLiveFromScheduleEnabledData } =
    useQuery<GetLiveFromScheduleEnabled>(GET_LIVE_FROM_SCHEDULE_ENABLED)

  const hasLiveFromSchedulePermission =
    getLiveFromScheduleEnabledData?.me?.uiPermissions
      ?.dashboardLiveFromScheduleEnabled ?? false

  const isToday = useIsToday(DateTime.fromISO(minTime))

  const handleOpenVideoBlade = useOpenVideoBlade({ appendParams: true })

  const onOpenLiveBlade = useCallback(
    (roomId: string) => {
      const timelineRoom = timelineState.rooms.find((r) => r.id === roomId)
      const inProgressApellaCase = timelineRoom?.status?.inProgressApellaCase
      const inProgressTurnover = timelineRoom?.status?.inProgressTurnover

      handleOpenVideoBlade(roomId, {
        apellaCase: inProgressApellaCase
          ? { ...inProgressApellaCase, type: CaseType.LIVE }
          : undefined,
        turnover: inProgressTurnover,
        analyticsEvent: EVENTS.SCHEDULE_PAGE_OPEN_VIDEO_BLADE,
        analyticsAddtlData: getAnalyticsAddtlTurnoverData(inProgressTurnover),
      })
    },
    [timelineState.rooms, handleOpenVideoBlade]
  )

  const [turnoverCount, wrapUpCount] = useMemo(
    () =>
      filteredTimelineState.rooms.reduce(
        ([turnoverCount, wrapUpCount], room) => {
          return [
            isToday && room.status.name === RoomStatusName.TURNOVER
              ? turnoverCount + 1
              : turnoverCount,
            isToday &&
            room.status.inProgressApellaCase?.status.name ===
              CaseStatusName.WRAP_UP
              ? wrapUpCount + 1
              : wrapUpCount,
          ]
        },
        [0, 0]
      ),
    [filteredTimelineState.rooms, isToday]
  )

  return (
    <>
      <SchedulePageHeader
        selectedSubNavId={'general'}
        sorts={[
          { key: RoomStatusName.TURNOVER, count: turnoverCount },
          { key: CaseStatusName.WRAP_UP, count: wrapUpCount },
        ]}
      />
      <ScheduleFilters />
      <ScheduleMetrics scheduleMetrics={scheduleMetrics} />
      {siteIds && (
        <div
          css={{
            paddingBottom: '10rem',
            pointerEvents: isAllEditPage ? 'none' : 'auto',
          }}
        >
          {filteredTimelineState.rooms.length === 0 ? (
            <EmptyRoomsMessage isLoading={filteredTimelineState.isLoading} />
          ) : (
            timelineHourBounds && (
              <TimelineView
                blocks={blocks ?? []}
                {...filteredTimelineState}
                {...timelineHourBounds}
                onClick={onClick}
                onTimeRangeChange={onChangeTimeRange}
                timeRange={timeRange}
                showLabels
                showScheduled={showScheduled}
                roomAction={(roomId: string) =>
                  hasLiveFromSchedulePermission &&
                  isToday && (
                    <Button
                      appearance={'link'}
                      onClick={() => onOpenLiveBlade(roomId)}
                      size={'sm'}
                    >
                      <Caps3>View live</Caps3>
                    </Button>
                  )
                }
                enableRightClick
              />
            )
          )}
        </div>
      )}
      {filteredTimelineState.isLoading ? <></> : <ToggleableCaseKey />}
    </>
  )
}

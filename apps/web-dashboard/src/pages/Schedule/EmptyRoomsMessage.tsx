import { useMemo } from 'react'

import { DateTime } from 'luxon'

import {
  Button,
  FlexContainer,
  H4,
  P2,
  remSpacing,
  Tile,
} from '@apella/component-library'
import { useTimezone } from 'src/Contexts'

import { useScheduleFilterContext } from './ScheduleFilterContext'

interface EmptyRoomsMessageProps {
  isLoading: boolean
}

export const EmptyRoomsMessage = ({ isLoading }: EmptyRoomsMessageProps) => {
  const { timezone } = useTimezone()
  const { minTime, showClosedRooms, onToggleShowClosedRooms } =
    useScheduleFilterContext()

  const messageInfo = useMemo(() => {
    if (isLoading) {
      return null
    }

    const selectedDate = DateTime.fromISO(minTime).setZone(timezone)
    const isToday = selectedDate.hasSame(
      DateTime.now().setZone(timezone),
      'day'
    )
    const isWeekend = selectedDate.weekday >= 6 // Saturday = 6, Sunday = 7
    const dayName = selectedDate.toFormat('cccc') // Full day name
    const dateFormatted = selectedDate.toFormat('MMMM d, yyyy')

    // If showing closed rooms and still no rooms, it's a different issue
    if (showClosedRooms) {
      return {
        type: 'no-rooms' as const,
        title: 'No rooms found',
        message: `No rooms are available for ${isToday ? 'today' : dateFormatted}.`,
        showToggle: false,
      }
    }

    // If hiding closed rooms and no rooms shown, we need to determine if it's because
    // all rooms are closed or if there are genuinely no rooms available.
    //
    // Note: When showClosedRooms is false, the GraphQL query filters out closed rooms
    // at the database level, so totalRoomsBeforeFiltering reflects the count after
    // the status filter has been applied. If totalRoomsBeforeFiltering is 0 when
    // hiding closed rooms, it means all rooms are closed (assuming there are rooms
    // configured for the selected sites).
    if (!showClosedRooms) {
      return {
        type: 'all-closed' as const,
        title: 'All rooms are closed',
        message: isToday
          ? 'All rooms are currently closed. You can show closed rooms to see the full schedule.'
          : `All rooms are closed on ${dayName}, ${dateFormatted}. ${isWeekend ? 'This is common on weekends. ' : ''
          }You can show closed rooms to see the full schedule.`,
        showToggle: true,
      }
    }

    return null
  }, [isLoading, minTime, timezone, showClosedRooms])

  if (!messageInfo) {
    return null
  }

  const handleShowClosedRooms = () => {
    onToggleShowClosedRooms(true)
  }

  return (
    <div
      css={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '300px',
        padding: remSpacing.large,
      }}
    >
      <Tile
        css={{
          maxWidth: '600px',
          textAlign: 'center',
        }}
        gutter={remSpacing.large}
      >
        <FlexContainer
          direction="column"
          gap={remSpacing.medium}
          alignItems="center"
        >
          <H4>{messageInfo.title}</H4>
          <P2>{messageInfo.message}</P2>
          {messageInfo.showToggle && (
            <Button
              appearance="button"
              onClick={handleShowClosedRooms}
              size="md"
            >
              Show closed rooms
            </Button>
          )}
        </FlexContainer>
      </Tile>
    </div>
  )
}
